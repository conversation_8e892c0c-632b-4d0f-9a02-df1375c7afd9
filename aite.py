import pandas as pd
import re
from collections import defaultdict

def extract_at_mentions(text):
    """
    从文本中提取@后面的用户名
    规则：
    1. 提取@后面的文本，只保留中文、英文、数字、下划线
    2. 如果@后面有冒号，则不提取该@
    3. 返回所有提取到的用户名列表
    """
    if pd.isna(text):
        return []

    # 查找所有@符号的位置
    mentions = []

    # 使用正则表达式查找@后面的用户名
    # @后面跟着的是中文、英文、数字、下划线的组合，直到遇到非这些字符为止
    pattern = r'@([a-zA-Z0-9_\u4e00-\u9fa5]+)'

    matches = re.finditer(pattern, text)

    for match in matches:
        mention_text = match.group(1)

        # 检查@后面是否紧跟着冒号，如果是则跳过
        after_mention = text[match.end():]
        if after_mention.startswith(':'):
            continue

        mentions.append(mention_text)

    return mentions

def create_mention_network():
    """
    创建@关系网络表
    """
    # 读取数据
    print("正在读取数据...")
    df = pd.read_csv('1.csv', encoding='gb18030')

    # 用于统计共现次数的字典
    mention_count = defaultdict(int)

    print("正在处理数据...")
    processed_count = 0

    # 遍历每一行数据
    for index, row in df.iterrows():
        author = row['原文作者']
        content = row['全文内容']

        # 跳过作者为空的行
        if pd.isna(author):
            continue

        # 提取@的用户名
        mentions = extract_at_mentions(content)

        # 为每个被@的用户创建记录
        for mentioned_user in mentions:
            # 创建(艾特者, 被艾特者)的键
            key = (author, mentioned_user)
            mention_count[key] += 1

        processed_count += 1
        if processed_count % 1000 == 0:
            print(f"已处理 {processed_count} 行数据...")

    print(f"数据处理完成，共处理 {processed_count} 行数据")
    print(f"找到 {len(mention_count)} 个唯一的@关系")

    # 创建结果DataFrame
    result_data = []
    for (author, mentioned_user), count in mention_count.items():
        result_data.append({
            '艾特者': author,
            '被艾特者': mentioned_user,
            '权重': count
        })

    result_df = pd.DataFrame(result_data)

    # 按权重降序排列
    result_df = result_df.sort_values('权重', ascending=False)

    return result_df

# 执行主函数
if __name__ == "__main__":
    print("开始创建@关系网络表...")

    # 创建@关系表
    mention_df = create_mention_network()

    # 保存结果
    output_file = '@关系网络表.csv'
    mention_df.to_csv(output_file, index=False, encoding='gb18030')

    print(f"\n结果已保存到: {output_file}")
    print(f"共生成 {len(mention_df)} 条@关系记录")

    # 显示前10条结果
    print("\n前10条@关系记录：")
    print(mention_df.head(10).to_string(index=False))

    # 显示一些统计信息
    print(f"\n统计信息：")
    print(f"总共有 {mention_df['艾特者'].nunique()} 个不同的艾特者")
    print(f"总共有 {mention_df['被艾特者'].nunique()} 个不同的被艾特者")
    print(f"最高权重: {mention_df['权重'].max()}")
    print(f"平均权重: {mention_df['权重'].mean():.2f}")