import pandas as pd
import re
from collections import defaultdict

# 验证函数：检查特定艾特者和被艾特者的关系
def verify_mention_count(author, mentioned_user):
    """
    验证特定艾特者和被艾特者的共现次数
    """
    print(f"\n=== 验证 '{author}' @'{mentioned_user}' 的共现次数 ===")

    # 读取原始数据
    df = pd.read_csv('1.csv', encoding='gb18030')

    # 筛选出该作者的所有记录
    author_records = df[df['原文作者'] == author]
    print(f"作者 '{author}' 总共有 {len(author_records)} 条记录")

    # 查找包含目标@用户的记录
    matching_records = author_records[author_records['全文内容'].str.contains(f'@{mentioned_user}', na=False)]
    print(f"包含 '@{mentioned_user}' 的记录有 {len(matching_records)} 条")

    # 详细分析每条记录
    count = 0
    for idx, row in matching_records.iterrows():
        content = row['全文内容']
        mentions = extract_at_mentions(content)
        target_count = mentions.count(mentioned_user)
        if target_count > 0:
            count += target_count
            print(f"第{idx}行: 找到 {target_count} 次 '@{mentioned_user}'")
            print(f"  内容: {content[:100]}...")
            print(f"  提取到的@用户: {mentions}")
            print("---")

    print(f"总计算出的共现次数: {count}")
    return count

def extract_at_mentions(text):
    """
    从文本中提取@后面的用户名
    规则：
    1. 提取@后面的文本，只保留中文、英文、数字、下划线
    2. 如果@后面有冒号，则不提取该@
    3. 返回所有提取到的用户名列表
    """
    if pd.isna(text):
        return []

    # 查找所有@符号的位置
    mentions = []

    # 使用正则表达式查找@后面的用户名
    # @后面跟着的是中文、英文、数字、下划线的组合，直到遇到非这些字符为止
    pattern = r'@([a-zA-Z0-9_\u4e00-\u9fa5]+)'

    matches = re.finditer(pattern, text)

    for match in matches:
        mention_text = match.group(1)

        # 检查@后面是否紧跟着冒号，如果是则跳过
        after_mention = text[match.end():]
        if after_mention.startswith(':'):
            continue

        mentions.append(mention_text)

    return mentions

def create_mention_network():
    """
    创建@关系网络表
    """
    # 读取数据
    print("正在读取数据...")
    df = pd.read_csv('1.csv', encoding='gb18030')

    # 用于统计共现次数的字典
    mention_count = defaultdict(int)

    print("正在处理数据...")
    processed_count = 0

    # 遍历每一行数据
    for index, row in df.iterrows():
        author = row['原文作者']
        content = row['全文内容']

        # 跳过作者为空的行
        if pd.isna(author):
            continue

        # 提取@的用户名
        mentions = extract_at_mentions(content)

        # 为每个被@的用户创建记录
        for mentioned_user in mentions:
            # 创建(艾特者, 被艾特者)的键
            key = (author, mentioned_user)
            mention_count[key] += 1

        processed_count += 1
        if processed_count % 1000 == 0:
            print(f"已处理 {processed_count} 行数据...")

    print(f"数据处理完成，共处理 {processed_count} 行数据")
    print(f"找到 {len(mention_count)} 个唯一的@关系")

    # 创建结果DataFrame
    result_data = []
    for (author, mentioned_user), count in mention_count.items():
        result_data.append({
            '艾特者': author,
            '被艾特者': mentioned_user,
            '权重': count
        })

    result_df = pd.DataFrame(result_data)

    # 按权重降序排列
    result_df = result_df.sort_values('权重', ascending=False)

    return result_df

# 执行主函数
if __name__ == "__main__":
    print("开始验证@关系计数...")

    # 先验证第一行数据
    verify_mention_count("'黑社会成员恶性报复实名举报者'", "广东公安")

    print("\n" + "="*50)
    print("分析可能的差异原因：")

    # 读取原始数据进行更详细的分析
    df = pd.read_csv('1.csv', encoding='gb18030')
    author_records = df[df['原文作者'] == "'黑社会成员恶性报复实名举报者'"]

    # 统计包含@广东公安的记录
    matching_records = author_records[author_records['全文内容'].str.contains('@广东公安', na=False)]

    print(f"该作者总共有 {len(author_records)} 条记录")
    print(f"包含'@广东公安'的记录有 {len(matching_records)} 条")

    # 检查是否有重复内容
    unique_contents = matching_records['全文内容'].nunique()
    print(f"其中唯一内容有 {unique_contents} 条")

    if len(matching_records) != unique_contents:
        print(f"发现 {len(matching_records) - unique_contents} 条重复内容")

        # 显示重复内容
        duplicate_contents = matching_records[matching_records.duplicated(subset=['全文内容'], keep=False)]
        print("\n重复的内容：")
        for content in duplicate_contents['全文内容'].unique():
            count = (duplicate_contents['全文内容'] == content).sum()
            print(f"重复{count}次: {content[:100]}...")

    # 检查同一条内容中多次@同一用户的情况
    multi_mention_count = 0
    for content in matching_records['全文内容']:
        mentions = extract_at_mentions(content)
        guangdong_count = mentions.count('广东公安')
        if guangdong_count > 1:
            multi_mention_count += guangdong_count - 1
            print(f"\n发现同一条内容中多次@广东公安 ({guangdong_count}次):")
            print(f"内容: {content[:150]}...")

    if multi_mention_count > 0:
        print(f"\n同一条内容中的额外@次数: {multi_mention_count}")

    print(f"\n总计算逻辑:")
    print(f"包含@广东公安的记录数: {len(matching_records)}")
    print(f"+ 同一条内容中的额外@次数: {multi_mention_count}")
    print(f"= 总共现次数: {len(matching_records) + multi_mention_count}")

    print("\n" + "="*50)
    print("开始创建@关系网络表...")

    # 创建@关系表
    mention_df = create_mention_network()

    # 保存结果
    output_file = '@关系网络表.csv'
    mention_df.to_csv(output_file, index=False, encoding='utf-8-sig')

    print(f"\n结果已保存到: {output_file}")
    print(f"共生成 {len(mention_df)} 条@关系记录")

    # 显示前10条结果
    print("\n前10条@关系记录：")
    print(mention_df.head(10).to_string(index=False))